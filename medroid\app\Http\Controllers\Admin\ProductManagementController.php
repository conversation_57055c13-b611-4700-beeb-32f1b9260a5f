<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ProductManagementController extends Controller
{
    public function index(Request $request)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $query = Product::with(['category', 'images'])
            ->orderBy('created_at', 'desc');

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $products = $query->paginate(20);
        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'products' => $products,
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Index', [
            'products' => $products,
            'categories' => $categories,
            'filters' => $request->only(['category', 'type', 'search']),
        ]);
    }

    public function create(Request $request)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Create', [
            'categories' => $categories,
        ]);
    }

    public function store(Request $request)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:physical,digital',
            'category_id' => 'required|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => 'required|string|max:255|unique:products,sku',
            'stock_quantity' => 'required_if:type,physical|integer|min:0',
            'manage_stock' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'digital_files' => 'required_if:type,digital|array',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
        ]);

        try {
            $product = Product::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'short_description' => $request->short_description,
                'type' => $request->type,
                'category_id' => $request->category_id,
                'price' => $request->price,
                'sale_price' => $request->sale_price,
                'sku' => $request->sku,
                'stock_quantity' => $request->type === 'physical' ? $request->stock_quantity : 0,
                'manage_stock' => $request->type === 'physical' ? ($request->manage_stock ?? true) : false,
                'in_stock' => $request->type === 'digital' ? true : ($request->stock_quantity > 0),
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
                'is_featured' => $request->is_featured ?? false,
                'is_active' => $request->is_active ?? true,
                'digital_files' => $request->type === 'digital' ? $request->digital_files : null,
                'download_limit' => $request->download_limit,
                'download_expiry_days' => $request->download_expiry_days,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'product' => $product->load(['category', 'images']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function show(Request $request, $id)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $product = Product::with(['category', 'images'])->findOrFail($id);

        if ($request->expectsJson()) {
            return response()->json([
                'product' => $product,
            ]);
        }

        return Inertia::render('Admin/Products/Show', [
            'product' => $product,
        ]);
    }

    public function edit(Request $request, $id)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $product = Product::with(['category', 'images'])->findOrFail($id);
        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'product' => $product,
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Edit', [
            'product' => $product,
            'categories' => $categories,
        ]);
    }

    public function update(Request $request, $id)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $product = Product::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:physical,digital',
            'category_id' => 'required|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => 'required|string|max:255|unique:products,sku,' . $id,
            'stock_quantity' => 'required_if:type,physical|integer|min:0',
            'manage_stock' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'digital_files' => 'required_if:type,digital|array',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
        ]);

        try {
            $product->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'short_description' => $request->short_description,
                'type' => $request->type,
                'category_id' => $request->category_id,
                'price' => $request->price,
                'sale_price' => $request->sale_price,
                'sku' => $request->sku,
                'stock_quantity' => $request->type === 'physical' ? $request->stock_quantity : 0,
                'manage_stock' => $request->type === 'physical' ? ($request->manage_stock ?? true) : false,
                'in_stock' => $request->type === 'digital' ? true : ($request->stock_quantity > 0),
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
                'is_featured' => $request->is_featured ?? false,
                'is_active' => $request->is_active ?? true,
                'digital_files' => $request->type === 'digital' ? $request->digital_files : null,
                'download_limit' => $request->download_limit,
                'download_expiry_days' => $request->download_expiry_days,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'product' => $product->load(['category', 'images']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Request $request, $id)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $product = Product::findOrFail($id);
            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function toggleStatus(Request $request, $id)
    {
        if (!Auth::user()->can('manage products') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $product = Product::findOrFail($id);
            $product->update(['is_active' => !$product->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Product status updated successfully',
                'product' => $product,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product status: ' . $e->getMessage(),
            ], 500);
        }
    }
}
